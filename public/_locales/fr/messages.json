{"addSearchProvider": {"message": "<PERSON><PERSON><PERSON> four<PERSON>"}, "contextMenuOpenAll": {"message": "<PERSON><PERSON> ouvrir"}, "contextMenuTitle": {"message": "Recherche inversée d'image"}, "extensionDescription": {"message": "Ajoute une option au menu contextuel pour éffectuer une recherche inversée d'une image sur une variété de services"}, "extensionName": {"message": "Reverse Image Search"}, "msgAtLeastOneSearchProvider": {"message": "<PERSON><PERSON><PERSON> de choisir au moins un fournisseur de recherche!"}, "msgDuplicatedProviderName": {"message": "Un nom de fournisseur de recherche dupliqué !"}, "msgIconUploadNotImage": {"message": "Veuillez télécharger un format d'image disponible ! (png, jpg, ico)"}, "msgIconUploadNotSquareImage": {"message": "<PERSON><PERSON><PERSON> de télécharger une image carrée"}, "msgSuccessSaveOptions": {"message": "Enregistrée!"}, "openInBackgroundLabel": {"message": "Ouvrir en arrière-plan"}, "openTabAtEnd": {"message": "A la fin"}, "openTabAtLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> la page de recherche"}, "openTabAtLeft": {"message": "A gauche de l'onglet actuel"}, "openTabAtRight": {"message": "A droite de l'onglet actuel"}, "optionsPageTitle": {"message": "Réglages"}, "providerNamePlaceholder": {"message": "2-15 lettres"}, "providerNamePlaceholderError": {"message": "Le nom du fournisseur $index$ est invalide. Merci d'utiliser 2-15 lettres.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholder": {"message": "http(s):// au début et %s pour l'url de l'image..."}, "providerURLPlaceholderError": {"message": "L'URL du fournisseur $index$ est invalide. Utiliser http(s):// au début et %s en tant qu'emplacement pour l'URL de l'image.", "placeholders": {"index": {"content": "$1"}}}, "restoreDefaultSearchProviders": {"message": "Restaurer par défaut"}, "saveOptions": {"message": "Enregistrer r<PERSON>s"}, "redirectingToSearchEngine": {"message": "Redirection vers le moteur de recherche..."}, "contentTypeLabel": {"message": "Type de Contenu"}, "doNotEncodeUrlLabel": {"message": "Ne pas encoder l'URL"}, "errorWhileSaving": {"message": "Une erreur inconnue s'est produite lors de la sauvegarde."}, "httpMethodLabel": {"message": "Méthode HTTP"}, "msgIconUploadNotSupported": {"message": "L'utilisation d'icônes dans les menus contextuels n'est pas prise en charge par ce navigateur."}, "postFieldNameLabel": {"message": "Nom du champ POST"}, "postFieldNamePlaceholder": {"message": "url"}, "providerURLPlaceholderErrorPOST": {"message": "L'URL du fournisseur $index$ est invalide. Utilisez http(s):// au début.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholderPOST": {"message": "http(s):// au début..."}, "searchAllByDefault": {"message": "Rechercher dans tous les fournisseurs par défaut"}, "searchAllByDefault_info": {"message": "Lorsque cette option est activée, une seule entrée de menu contextuel est affichée et cliquer dessus ouvrira tous les fournisseurs de recherche sélectionnés. Si cette option est désactivée, un sous-menu sera créé."}, "showOpenAll": {"message": "<PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON>'"}, "showOpenAllAtTop": {"message": "A<PERSON><PERSON><PERSON> '<PERSON><PERSON>' en haut"}, "stripProtocolLabel": {"message": "Supprimer le protocole de l'URL de l'image"}, "privacyPolicy": {"message": "Politique de Confidentialité"}, "version": {"message": "Version $version$", "placeholders": {"version": {"content": "$1"}}}, "consentDialogTitle": {"message": "Consentement de Collecte de Données"}, "consentDataCollectionTitle": {"message": "Que fait cette extension ?"}, "consentDataCollectionDescription": {"message": "Cette extension vous permet de rechercher des images sur divers moteurs de recherche en partageant les URLs d'images avec des services externes. Pour plus d'informations, consultez notre <a href=\"https://p.nyanya.de/reverse-image-search-privacy-policy.html\" target=\"_blank\">politique de confidentialité</a>."}, "consentWhatDataTitle": {"message": "Quelles données sont collectées et transmises ?"}, "consentDataImageUrls": {"message": "URLs d'images des pages web que vous choisissez de rechercher"}, "consentDataSearchServices": {"message": "Ces URLs sont envoyées aux services de recherche externes (Google, Bing, etc.) lorsque vous utilisez le menu contextuel"}, "consentHowDataUsedTitle": {"message": "Comment ces données sont-elles utilisées ?"}, "consentHowDataUsedDescription": {"message": "Les URLs d'images ne sont utilisées que pour effectuer des recherches d'images inversées sur les services que vous sélectionnez. Cette extension ne stocke aucune donnée."}, "consentAccept": {"message": "Accept<PERSON> et Continuer"}, "consentDeclineAndUninstall": {"message": "Refuser et Désinstaller"}}