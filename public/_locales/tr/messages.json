{"addSearchProvider": {"message": "Sağlayıcı Ekle"}, "contextMenuOpenAll": {"message": "<PERSON><PERSON><PERSON> A<PERSON>"}, "contextMenuTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "extensionDescription": {"message": "Çeşitli servisler aracılığıyla tersinden görsel arama için bağlam menüsüne bir seçenek ekler."}, "extensionName": {"message": "Reverse Image Search"}, "msgAtLeastOneSearchProvider": {"message": "Lütfen en az bir arama sağlayıcı seçin!"}, "msgDuplicatedProviderName": {"message": "Bazı arama sağlayıcılarının adı aynı!"}, "msgIconUploadNotImage": {"message": "Lütfen geçerli bir resim formatı yükleyin! (png, jpg, ico)"}, "msgIconUploadNotSquareImage": {"message": "Yüklenen görsel kare olmalı!"}, "msgSuccessSaveOptions": {"message": "<PERSON><PERSON><PERSON><PERSON>!"}, "openInBackgroundLabel": {"message": "Arka planda aç"}, "openTabAtEnd": {"message": "En sonda"}, "openTabAtLabel": {"message": "Arama sayfasını aç"}, "openTabAtLeft": {"message": "Geç<PERSON><PERSON> sek<PERSON> so<PERSON>a"}, "openTabAtRight": {"message": "Geçerli sekmenin <PERSON>"}, "optionsPageTitle": {"message": "Seçenekler"}, "providerNamePlaceholder": {"message": "2-15 harf"}, "providerNamePlaceholderError": {"message": "$index$ numaralı sağlayıcının adı geçersiz. Lütfen 2-15 harf kullanın.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholder": {"message": "http(s):// ile başlayın ve resim url'si için %s kullanın..."}, "providerURLPlaceholderError": {"message": "$index$ sağlayıcısının URL'si geçersiz. URL'ye http(s):// ile başlayın ve yer tutucu olarak %s kullanın.", "placeholders": {"index": {"content": "$1"}}}, "restoreDefaultSearchProviders": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "saveOptions": {"message": "<PERSON><PERSON>"}, "redirectingToSearchEngine": {"message": "Arama motoruna yönlendiriliyor..."}, "contentTypeLabel": {"message": "İçerik Türü"}, "doNotEncodeUrlLabel": {"message": "URL'yi kodlama"}, "errorWhileSaving": {"message": "Kaydederken bilinmeyen bir hata oluş<PERSON>."}, "httpMethodLabel": {"message": "HTTP Yöntemi"}, "msgIconUploadNotSupported": {"message": "Bağlam menülerinde simge kullanımı bu tarayıcı tarafından desteklenmiyor."}, "postFieldNameLabel": {"message": "POST alan adı"}, "postFieldNamePlaceholder": {"message": "url"}, "providerURLPlaceholderErrorPOST": {"message": "Sağlayıcı $index$ URL'si geçersiz. Başlangıçta http(s):// kullanın.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholderPOST": {"message": "Başlangıçta http(s)://..."}, "searchAllByDefault": {"message": "Varsayılan olarak tüm sağlayıcılarda ara"}, "searchAllByDefault_info": {"message": "Bu seçenek etkinleştirildiğinde, yaln<PERSON>zca bir bağlam menüsü girişi gösterilir ve tıklandığında seçilen tüm arama sağlayıcıları açılır. Bu devre dışı bırakılırsa, bir alt menü oluşturulur."}, "showOpenAll": {"message": "'Hepsini Aç'ı göster"}, "showOpenAllAtTop": {"message": "'Hepsini Aç'ı üstte göster"}, "stripProtocolLabel": {"message": "Görsel URL'sinden protokolü kaldır"}, "privacyPolicy": {"message": "Gizlilik Politikası"}, "version": {"message": "Sürüm $version$", "placeholders": {"version": {"content": "$1"}}}, "consentDialogTitle": {"message": "Veri Toplama Onayı"}, "consentDataCollectionTitle": {"message": "Bu uzantı ne yapar?"}, "consentDataCollectionDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, görsel URL'lerini harici hizmetlerle paylaşarak çeşitli arama motorlarında görsel araması yapmanızı sağlar. Daha fazla bilgi için <a href=\"https://p.nyanya.de/reverse-image-search-privacy-policy.html\" target=\"_blank\">gizlilik politikamızı</a> inceleyin."}, "consentWhatDataTitle": {"message": "Hangi veriler top<PERSON>ır ve iletilir?"}, "consentDataImageUrls": {"message": "Arama yapmayı seçtiğiniz web sayfalarından görsel URL'leri"}, "consentDataSearchServices": {"message": "Bağlam menüsünü kullandığınızda bu URL'ler harici arama <PERSON> (Google, Bing, vb.) gönderilir"}, "consentHowDataUsedTitle": {"message": "Bu veriler nasıl kullanılır?"}, "consentHowDataUsedDescription": {"message": "Görsel URL'leri yalnızca seçtiğiniz hizmetlerde ters görsel arama yapmak için kullanılır. Bu uzantı hiçbir veri saklamaz."}, "consentAccept": {"message": "Kabul Et ve <PERSON>am Et"}, "consentDeclineAndUninstall": {"message": "Reddet ve Kaldır"}}