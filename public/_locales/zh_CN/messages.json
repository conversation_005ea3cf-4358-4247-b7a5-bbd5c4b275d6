{"addSearchProvider": {"message": "添加提供商"}, "contextMenuOpenAll": {"message": "全部打开"}, "contextMenuTitle": {"message": "搜索该图片"}, "extensionDescription": {"message": "添加一组支持通过多个提供商搜索的右键菜单"}, "extensionName": {"message": "Reverse Image Search"}, "msgAtLeastOneSearchProvider": {"message": "请选择至少一个搜索提供商！"}, "msgDuplicatedProviderName": {"message": "部分搜索提供商的名称重复！"}, "msgIconUploadNotImage": {"message": "请上传可用的图片格式 (png, jpg, ico)"}, "msgIconUploadNotSquareImage": {"message": "请上传矩形图片！"}, "msgSuccessSaveOptions": {"message": "已保存！"}, "openInBackgroundLabel": {"message": "标签页打开在后台"}, "openTabAtEnd": {"message": "标签栏末尾"}, "openTabAtLabel": {"message": "打开搜索页面"}, "openTabAtLeft": {"message": "当前标签页左侧"}, "openTabAtRight": {"message": "当前标签页右侧"}, "optionsPageTitle": {"message": "选项"}, "providerNamePlaceholder": {"message": "2-15 个字母"}, "providerNamePlaceholderError": {"message": "第 $index$ 个提供商的名称无效。请使用 2-15 个字母", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholder": {"message": "应以 http(s):// 开头，并用 %s 表示图片网址位置..."}, "providerURLPlaceholderError": {"message": "第 $index$ 个提供商的网址无效。请以 http(s):// 开头，并用 %s 表示图片网址位置。", "placeholders": {"index": {"content": "$1"}}}, "restoreDefaultSearchProviders": {"message": "恢复默认设置"}, "saveOptions": {"message": "保存选项"}, "searchAllByDefault": {"message": "自动通过所有提供商搜索"}, "searchAllByDefault_info": {"message": "启用此选项后，只会显示一个上下文菜单条目，单击它将打开所有选定提供商的搜索页。 如果禁用该选项，将会创建一个子菜单来显示所有选定的搜索提供商。"}, "showOpenAll": {"message": "显示'全部打开'"}, "showOpenAllAtTop": {"message": "将'全部打开'选项置顶"}, "redirectingToSearchEngine": {"message": "正在重定向到搜索引擎..."}, "contentTypeLabel": {"message": "内容类型"}, "doNotEncodeUrlLabel": {"message": "不编码URL"}, "errorWhileSaving": {"message": "保存时发生未知错误。"}, "httpMethodLabel": {"message": "HTTP方法"}, "msgIconUploadNotSupported": {"message": "此浏览器不支持在上下文菜单中使用图标。"}, "postFieldNameLabel": {"message": "POST字段名"}, "postFieldNamePlaceholder": {"message": "url"}, "providerURLPlaceholderErrorPOST": {"message": "提供商 $index$ 的URL无效。请在开头使用http(s)://。", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholderPOST": {"message": "开头使用http(s)://..."}, "stripProtocolLabel": {"message": "从图片URL中移除协议"}, "privacyPolicy": {"message": "隐私政策"}, "version": {"message": "版本 $version$", "placeholders": {"version": {"content": "$1"}}}}