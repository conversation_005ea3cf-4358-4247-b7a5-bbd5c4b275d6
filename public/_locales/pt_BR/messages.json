{"addSearchProvider": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "contextMenuOpenAll": {"message": "<PERSON><PERSON><PERSON> todos"}, "contextMenuTitle": {"message": "Pesquisa Reversa de Imagens"}, "extensionDescription": {"message": "Adicionar uma opção ao menu de contexto para fazer uma busca reversa de uma imagem em vários serviços"}, "extensionName": {"message": "Pesquisa Reversa de Imagens"}, "msgAtLeastOneSearchProvider": {"message": "Por favor escolha pelo menos um provedor de busca!"}, "msgDuplicatedProviderName": {"message": "Algum provedor de busca está com o nome duplicado!"}, "msgIconUploadNotImage": {"message": "Por favor envie um formato de imagem válido! (png, jpg,ico)"}, "msgIconUploadNotSquareImage": {"message": "Por favor envie uma imagem quadrada!"}, "msgSuccessSaveOptions": {"message": "Salvo!"}, "openInBackgroundLabel": {"message": "Abrir em Segundo Plano"}, "openTabAtEnd": {"message": "No fim"}, "openTabAtLabel": {"message": "Abrir a página de busca"}, "openTabAtLeft": {"message": "A esquerda da guia atual"}, "openTabAtRight": {"message": "A direita da guia atual"}, "optionsPageTitle": {"message": "Opções"}, "providerNamePlaceholder": {"message": "2-15 letras"}, "providerNamePlaceholderError": {"message": "O nome do provedor $index$ é inválido. Por favor use de 2-15 letras.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholder": {"message": "http(s):// no início e %s para a url da imagem..."}, "providerURLPlaceholderError": {"message": "A url do provedor $index$ é invalido. Use http(s):// no inicio e %s como espaço reservado para a url da imagem.", "placeholders": {"index": {"content": "$1"}}}, "restoreDefaultSearchProviders": {"message": "<PERSON><PERSON><PERSON>"}, "saveOptions": {"message": "<PERSON><PERSON>"}, "redirectingToSearchEngine": {"message": "Redirecionando para o mecanismo de busca..."}, "contentTypeLabel": {"message": "Tipo de Conteúdo"}, "doNotEncodeUrlLabel": {"message": "Não codificar URL"}, "errorWhileSaving": {"message": "Houve um erro desconhecido ao salvar."}, "httpMethodLabel": {"message": "Método HTTP"}, "msgIconUploadNotSupported": {"message": "O uso de ícones em menus contextuais não é suportado por este navegador."}, "postFieldNameLabel": {"message": "Nome do campo POST"}, "postFieldNamePlaceholder": {"message": "url"}, "providerURLPlaceholderErrorPOST": {"message": "A URL do provedor $index$ é inválida. Use http(s):// no início.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholderPOST": {"message": "http(s):// no início..."}, "searchAllByDefault": {"message": "Pesquisar em todos os provedores por padrão"}, "searchAllByDefault_info": {"message": "Quando esta opção está habilitada, apenas uma entrada do menu contextual é mostrada e clicar nela abrirá todos os provedores de pesquisa selecionados. Se estiver desabilitada, um submenu será criado."}, "showOpenAll": {"message": "<PERSON>rar '<PERSON><PERSON><PERSON>'"}, "showOpenAllAtTop": {"message": "Mostrar '<PERSON><PERSON><PERSON>' no topo"}, "stripProtocolLabel": {"message": "Remover protocolo da URL da imagem"}, "privacyPolicy": {"message": "Política de Privacidade"}, "version": {"message": "Versão $version$", "placeholders": {"version": {"content": "$1"}}}, "consentDialogTitle": {"message": "Consentimento de Coleta de Dados"}, "consentDataCollectionTitle": {"message": "O que esta extensão faz?"}, "consentDataCollectionDescription": {"message": "Esta extensão permite pesquisar imagens em vários mecanismos de busca compartilhando URLs de imagens com serviços externos."}, "consentWhatDataTitle": {"message": "Quais dados são coletados e transmitidos?"}, "consentDataImageUrls": {"message": "URLs de imagens de páginas web que você escolhe pesquisar"}, "consentDataSearchServices": {"message": "Essas URLs são enviadas para serviços de busca externos (Google, Bing, etc.) quando você usa o menu de contexto"}, "consentHowDataUsedTitle": {"message": "Como esses dados são usados?"}, "consentHowDataUsedDescription": {"message": "As URLs de imagens são usadas apenas para realizar pesquisas reversas de imagens nos serviços que você selecionar. Esta extensão não armazena dados."}, "consentRequiredNote": {"message": "Seu consentimento é necessário para usar esta extensão. Você pode alterar esta configuração a qualquer momento nas opções da extensão."}, "consentAccept": {"message": "Aceitar e Continuar"}, "consentDeclineAndUninstall": {"message": "Recusar e Desinstalar"}}