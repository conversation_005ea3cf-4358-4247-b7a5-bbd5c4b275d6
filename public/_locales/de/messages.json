{"addSearchProvider": {"message": "An<PERSON><PERSON> hinzufügen"}, "contextMenuOpenAll": {"message": "Alle öffnen"}, "contextMenuTitle": {"message": "Bildersuche"}, "errorWhileSaving": {"message": "<PERSON><PERSON> ist ein unbekannter Fehler aufgetreten."}, "extensionDescription": {"message": "<PERSON>ügt eine Option zum Kontextmenü hinzu, um mit einem Bild auf verschiedenen Diensten zu suchen"}, "extensionName": {"message": "Bilder-Rückwärtssuche"}, "msgAtLeastOneSearchProvider": {"message": "Bitte wähle mindestens einen Suchanbieter!"}, "msgDuplicatedProviderName": {"message": "Ein Suchanbieter-Name ist doppelt!"}, "msgIconUploadNotImage": {"message": "Bitte lade ein unterstütztes Format hoch! (png, jpg, ico)"}, "msgIconUploadNotSquareImage": {"message": "Bitte lade ein quadratisches Bild hoch!"}, "msgIconUploadNotSupported": {"message": "Die Verwendung von Icons in Kontextmenüs wird von diesem Browser nicht unterstützt."}, "msgSuccessSaveOptions": {"message": "Gespeichert!"}, "openInBackgroundLabel": {"message": "<PERSON><PERSON>"}, "openTabAtEnd": {"message": "<PERSON>"}, "openTabAtLabel": {"message": "Suchseite öffnen"}, "openTabAtLeft": {"message": "Links nebem dem aktuellen Tab"}, "openTabAtRight": {"message": "Rechts neben dem aktuellen Tab"}, "optionsPageTitle": {"message": "Optionen"}, "providerNamePlaceholder": {"message": "2-15 <PERSON><PERSON><PERSON>"}, "providerNamePlaceholderError": {"message": "Der Name des $index$. Anbie<PERSON> ist ungültig. Bitte nur 2-15 <PERSON>eichen.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholder": {"message": "http(s):// am Anfang und %s für die Bilder-URL..."}, "providerURLPlaceholderPOST": {"message": "http(s):// am Anfang..."}, "providerURLPlaceholderError": {"message": "Die URL des $index$. Anbieters ist ungültig. Nutze http(s):// am Anfang und %s als Platzhalter für die Bilder-URL.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholderErrorPOST": {"message": "Die URL des $index$. Anbieters ist ungültig. Nutze http(s):// am Anfang.", "placeholders": {"index": {"content": "$1"}}}, "doNotEncodeUrlLabel": {"message": "URL nicht enkodieren"}, "stripProtocolLabel": {"message": "Protokoll aus Bilder-URL entfernen"}, "httpMethodLabel": {"message": "HTTP-Methode"}, "postFieldNameLabel": {"message": "POST-Feldname"}, "postFieldNamePlaceholder": {"message": "url"}, "contentTypeLabel": {"message": "Content-Type"}, "restoreDefaultSearchProviders": {"message": "Standard wiederherstellen"}, "saveOptions": {"message": "Optionen speichern"}, "searchAllByDefault": {"message": "Standardmäßig alle Suchanbieter durchsuchen"}, "searchAllByDefault_info": {"message": "Wenn diese Option aktiv ist, wird nur ein Menüeintrag angezeigt, der beim Anklicken alle Suchanbieter öffnet. Wenn diese Option deaktiviert ist, wird ein Untermenü erstellt."}, "showOpenAll": {"message": "'Alle öffnen' anzeigen"}, "showOpenAllAtTop": {"message": "'Alle öffnen' als Erstes anzeigen"}, "redirectingToSearchEngine": {"message": "Weiterleitung zur Suchmaschine..."}, "privacyPolicy": {"message": "Datenschutzerklärung"}, "version": {"message": "Version $version$", "placeholders": {"version": {"content": "$1"}}}, "consentDialogTitle": {"message": "Einverständnis zur Datenerhebung"}, "consentDataCollectionTitle": {"message": "Was macht diese Erweiterung?"}, "consentDataCollectionDescription": {"message": "Diese Erweiterung ermöglicht es dir, Bilder in verschiedenen Suchmaschinen zu suchen, indem Bild-URLs an externe Dienste weitergegeben werden. Weitere Informationen findest du in unserer <a href=\"https://p.nyanya.de/reverse-image-search-privacy-policy.html\" target=\"_blank\">Datenschutzerklärung</a>."}, "consentWhatDataTitle": {"message": "Welche Daten werden gesammelt und übertragen?"}, "consentDataImageUrls": {"message": "Bild-U<PERSON><PERSON> von <PERSON>, die du zum Suchen auswählst"}, "consentDataSearchServices": {"message": "Diese URLs werden an externe Suchdienste (Google, Bing, etc.) gese<PERSON><PERSON>, wenn du das Kontextmenü verwendest"}, "consentHowDataUsedTitle": {"message": "Wie werden diese Daten verwendet?"}, "consentHowDataUsedDescription": {"message": "Die Bild-URLs werden nur verwendet, um umgekehrte Bildsuchen in den von dir ausgewählten Diensten durchzuführen. Diese Erweiterung speichert keine Daten."}, "consentAccept": {"message": "Akzeptieren und Fortfahren"}, "consentDeclineAndUninstall": {"message": "Ablehnen und Deinstallieren"}}