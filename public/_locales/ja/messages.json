{"addSearchProvider": {"message": "検索エンジンを追加"}, "contextMenuOpenAll": {"message": "すべて開く"}, "contextMenuTitle": {"message": "画像で検索"}, "extensionDescription": {"message": "コンテキストメニューにオプションを追加することで、さまざまなサービスで類似した画像の検索ができます"}, "extensionName": {"message": "Reverse Image Search"}, "msgAtLeastOneSearchProvider": {"message": "少なくとも1つの検索エンジンを選択してください！"}, "msgDuplicatedProviderName": {"message": "一部の検索エンジン名が重複しています！"}, "msgIconUploadNotImage": {"message": "利用可能な画像フォーマットでアップロードしてください！ (png, jpg, ico)"}, "msgIconUploadNotSquareImage": {"message": "正方形の画像をアップロードしてください！"}, "msgSuccessSaveOptions": {"message": "保存しました！"}, "openInBackgroundLabel": {"message": "バックグラウンドで開く"}, "openTabAtEnd": {"message": "最後に"}, "openTabAtLabel": {"message": "検索ページを開く"}, "openTabAtLeft": {"message": "現在のタブの左"}, "openTabAtRight": {"message": "現在のタブの右"}, "optionsPageTitle": {"message": "オプション"}, "providerNamePlaceholder": {"message": "2～15文字"}, "providerNamePlaceholderError": {"message": "検索エンジン $index$ の名前が無効です。2～15文字を使用してください", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholder": {"message": "http(s)://が始まり、画像のURLの代わりに%sが..."}, "providerURLPlaceholderError": {"message": "$index$番目の検索エンジンのURLは無効です。http(s)://が始まり、画像URLのプレースホルダーとして\"%s \"を使用してください", "placeholders": {"index": {"content": "$1"}}}, "restoreDefaultSearchProviders": {"message": "デフォルトを復元"}, "saveOptions": {"message": "オプションを保存"}, "redirectingToSearchEngine": {"message": "検索エンジンにリダイレクト中..."}, "contentTypeLabel": {"message": "コンテンツタイプ"}, "doNotEncodeUrlLabel": {"message": "URLをエンコードしない"}, "errorWhileSaving": {"message": "保存中に不明なエラーが発生しました。"}, "httpMethodLabel": {"message": "HTTPメソッド"}, "msgIconUploadNotSupported": {"message": "このブラウザではコンテキストメニューでのアイコンの使用はサポートされていません。"}, "postFieldNameLabel": {"message": "POSTフィールド名"}, "postFieldNamePlaceholder": {"message": "url"}, "providerURLPlaceholderErrorPOST": {"message": "検索エンジン $index$ のURLが無効です。http(s)://で始めてください。", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholderPOST": {"message": "http(s)://で始める..."}, "searchAllByDefault": {"message": "デフォルトですべての検索エンジンを検索"}, "searchAllByDefault_info": {"message": "このオプションが有効な場合、コンテキストメニューには1つのエントリのみが表示され、クリックすると選択されたすべての検索エンジンが開きます。無効にすると、サブメニューが作成されます。"}, "showOpenAll": {"message": "'すべて開く'を表示"}, "showOpenAllAtTop": {"message": "'すべて開く'を上部に表示"}, "stripProtocolLabel": {"message": "画像URLからプロトコルを削除"}, "privacyPolicy": {"message": "プライバシーポリシー"}, "version": {"message": "バージョン $version$", "placeholders": {"version": {"content": "$1"}}}, "consentDialogTitle": {"message": "データ収集の同意"}, "consentDataCollectionTitle": {"message": "この拡張機能は何をしますか？"}, "consentDataCollectionDescription": {"message": "この拡張機能は、画像URLを外部サービスと共有することで、さまざまな検索エンジンで画像を検索できるようにします。"}, "consentWhatDataTitle": {"message": "どのようなデータが収集・送信されますか？"}, "consentDataImageUrls": {"message": "検索を選択したウェブページの画像URL"}, "consentDataSearchServices": {"message": "コンテキストメニューを使用すると、これらのURLが外部検索サービス（Google、Bingなど）に送信されます"}, "consentHowDataUsedTitle": {"message": "このデータはどのように使用されますか？"}, "consentHowDataUsedDescription": {"message": "画像URLは、選択したサービスで逆画像検索を実行するためにのみ使用されます。この拡張機能はデータを保存しません。"}, "consentRequiredNote": {"message": "この拡張機能を使用するには同意が必要です。拡張機能のオプションでいつでもこの設定を変更できます。"}, "consentAccept": {"message": "同意して続行"}, "consentDeclineAndUninstall": {"message": "拒否してアンインストール"}}