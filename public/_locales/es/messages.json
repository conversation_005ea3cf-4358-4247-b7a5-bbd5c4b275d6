{"addSearchProvider": {"message": "<PERSON><PERSON><PERSON>"}, "contextMenuOpenAll": {"message": "<PERSON><PERSON><PERSON> todos"}, "contextMenuTitle": {"message": "Búsqueda inversa de imagen"}, "extensionDescription": {"message": "Añade una opción al menú contextual para buscar inversamente una imagen en varios servicios"}, "extensionName": {"message": "Búsqueda inversa de imagen"}, "msgAtLeastOneSearchProvider": {"message": "Por favor escoja al menos un proveedor de búsquedas!"}, "msgDuplicatedProviderName": {"message": "Algún nombre de proveedor está duplicado!"}, "msgIconUploadNotImage": {"message": "Por favor suba formatos admitidos! (png, jpg, ico)"}, "msgIconUploadNotSquareImage": {"message": "Por favor suba una imagen rectangular!"}, "msgSuccessSaveOptions": {"message": "Guardado!"}, "openInBackgroundLabel": {"message": "Abrir en segundo plano"}, "openTabAtEnd": {"message": "Al final"}, "openTabAtLabel": {"message": "Abrir la página de búsquedas"}, "openTabAtLeft": {"message": "A la izquierda de la pestaña actual"}, "openTabAtRight": {"message": "A la derecha de la pestaña actual"}, "optionsPageTitle": {"message": "Opciones"}, "providerNamePlaceholder": {"message": "2-15 letras"}, "providerNamePlaceholderError": {"message": "El nombre del proveedor $index$ es inválido. Por favor use de 2-15 letras.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholder": {"message": "http(s):// al comienzo y %s como ubicador para la url de la imagen..."}, "providerURLPlaceholderError": {"message": "El url del proveedor $index$ es inválido. Use http(s):// al inicio y %s como ubicador para el url de la imagen.", "placeholders": {"index": {"content": "$1"}}}, "restoreDefaultSearchProviders": {"message": "Restaurar predeterminados"}, "saveOptions": {"message": "Guardar opciones"}, "redirectingToSearchEngine": {"message": "Redirigiendo al motor de búsqueda..."}, "contentTypeLabel": {"message": "Tipo de Contenido"}, "doNotEncodeUrlLabel": {"message": "No codificar URL"}, "errorWhileSaving": {"message": "Hubo un error desconocido al guardar."}, "httpMethodLabel": {"message": "Método HTTP"}, "msgIconUploadNotSupported": {"message": "El uso de iconos en menús contextuales no es compatible con este navegador."}, "postFieldNameLabel": {"message": "Nombre del campo POST"}, "postFieldNamePlaceholder": {"message": "url"}, "providerURLPlaceholderErrorPOST": {"message": "La URL del proveedor $index$ es inválida. Use http(s):// al inicio.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholderPOST": {"message": "http(s):// al inicio..."}, "searchAllByDefault": {"message": "Buscar en todos los proveedores por defecto"}, "searchAllByDefault_info": {"message": "Cuando esta opción está habilitada, solo se muestra una entrada en el menú contextual y al hacer clic se abrirán todos los proveedores de búsqueda seleccionados. Si está deshabilitada, se creará un submenú."}, "showOpenAll": {"message": "<PERSON>rar '<PERSON><PERSON><PERSON>'"}, "showOpenAllAtTop": {"message": "Mostrar 'Abrir <PERSON>' en la parte superior"}, "stripProtocolLabel": {"message": "Eliminar protocolo de la URL de la imagen"}, "privacyPolicy": {"message": "Política de Privacidad"}, "version": {"message": "Versión $version$", "placeholders": {"version": {"content": "$1"}}}}