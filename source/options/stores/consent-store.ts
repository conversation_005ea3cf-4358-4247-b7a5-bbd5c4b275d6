import { writable } from 'svelte/store';
import { getOptions } from '../../storage';

interface ConsentState {
  consentGiven: boolean;
  consentVersion: string;
  needsConsent: boolean;
}

const createConsentStore = () => {
  const { subscribe, set, update } = writable<ConsentState>({
    consentGiven: false,
    consentVersion: '',
    needsConsent: false,
  });

  return {
    subscribe,
    set,
    checkConsentNeeded: async () => {
      const options = await getOptions();
      const currentVersion = chrome.runtime.getManifest().version;
      const needsConsent = !options.consentGiven || options.consentVersion !== currentVersion;
      
      update(() => ({
        consentGiven: options.consentGiven,
        consentVersion: options.consentVersion,
        needsConsent,
      }));
      
      return needsConsent;
    },
    giveConsent: async () => {
      const currentVersion = chrome.runtime.getManifest().version;
      const options = await getOptions();
      
      const updatedOptions = {
        ...options,
        consentGiven: true,
        consentVersion: currentVersion,
      };
      
      await chrome.storage.sync.set(updatedOptions);
      
      update(() => ({
        consentGiven: true,
        consentVersion: currentVersion,
        needsConsent: false,
      }));
    },
    declineAndUninstall: async () => {
      await chrome.management.uninstallSelf();
    },
  };
};

export const consentStore = createConsentStore();
