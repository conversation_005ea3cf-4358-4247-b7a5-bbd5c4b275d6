<script lang="ts">
  import { getMessage } from '../../utils';
  import { consentStore } from '../stores/consent-store';

  const handleAccept = async () => {
    await consentStore.giveConsent();
  };

  const handleDecline = async () => {
    await consentStore.declineAndUninstall();
  };
</script>

<div class="modal d-block" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{getMessage('consentDialogTitle')}</h5>
      </div>
      <div class="modal-body">
        <div class="alert alert-info">
          <h6>{getMessage('consentDataCollectionTitle')}</h6>
          <p>{getMessage('consentDataCollectionDescription')}</p>
          
          <h6>{getMessage('consentWhatDataTitle')}</h6>
          <ul>
            <li>{getMessage('consentDataImageUrls')}</li>
            <li>{getMessage('consentDataSearchServices')}</li>
          </ul>
          
          <h6>{getMessage('consentHowDataUsedTitle')}</h6>
          <p>{getMessage('consentHowDataUsedDescription')}</p>
          
          <p class="mb-0">
            <a href="https://p.nyanya.de/reverse-image-search-privacy-policy.html" 
               target="_blank" 
               rel="noopener noreferrer">
              {getMessage('privacyPolicy')}
            </a>
          </p>
        </div>
        
        <p class="text-muted small">{getMessage('consentRequiredNote')}</p>
      </div>
      <div class="modal-footer">
        <button type="button" 
                class="btn btn-danger" 
                on:click={handleDecline}>
          {getMessage('consentDeclineAndUninstall')}
        </button>
        <button type="button" 
                class="btn btn-primary" 
                on:click={handleAccept}>
          {getMessage('consentAccept')}
        </button>
      </div>
    </div>
  </div>
</div>

<style>
  .modal {
    background-color: rgba(0, 0, 0, 0.5);
  }
</style>
