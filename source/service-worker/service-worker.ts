import { onReverseSearch, setupContextMenu } from './service-worker-functions';
import { getOptions } from '../storage';

chrome.runtime.onInstalled.addListener(async (details) => {
  await setupContextMenu();

  if (details.reason === 'install') {
    await chrome.runtime.openOptionsPage();
  } else if (details.reason === 'update') {
    const { consentGiven } = await getOptions();
    if (!consentGiven) {
      const url = chrome.runtime.getURL('options.html');
      await chrome.tabs.create({ url });
    }
  }
});
chrome.storage.sync.onChanged.addListener(setupContextMenu);

chrome.action.onClicked.addListener(() => chrome.runtime.openOptionsPage());

chrome.contextMenus.onClicked.addListener(onReverseSearch);
