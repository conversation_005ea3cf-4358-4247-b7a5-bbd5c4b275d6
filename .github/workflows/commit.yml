name: Build commit

on:
  - push
  - workflow_dispatch

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest

    steps:
      - name: <PERSON><PERSON>
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install dependencies
        run: npm ci

      - name: Test
        run: npm run test

      - name: Lint
        run: |
          npm run lint:types
          npm run lint:code
          npm run lint:web-ext

      - name: Build
        run: npm run build

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: Reverse-Image-Search-${{github.sha}}
          path: public/*
          if-no-files-found: error
