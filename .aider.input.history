
# 2025-06-13 12:20:44.652662
+/add source/options/components/Provider.svelte

# 2025-06-13 12:20:54.329441
+/add source/options/

# 2025-06-13 12:21:22.520124
+The user should be able to configure POST requests for search engines.

# 2025-06-13 12:21:32.028976
+/quit

# 2025-06-13 12:22:09.090389
+Support POST requests for search engines.
+

# 2025-06-13 12:22:21.002789
+The user should be able to configure POST requests for search engines.

# 2025-06-13 12:23:35.091373
+A

# 2025-06-13 12:25:44.426908
+D

# 2025-06-13 12:25:58.118523
+/add source/storage.ts

# 2025-06-13 12:26:00.609306
+go on

# 2025-06-13 12:29:31.969864
+/run pnpm run build

# 2025-06-13 12:29:31.969987
+/run pnpm run build

# 2025-06-13 12:29:34.244173
+/run pnpm test

# 2025-06-13 12:29:34.244268
+/run pnpm test

# 2025-06-13 12:35:30.745024
+It should be possible to also configure POST requests. Also, don't check for %s in the URL in this case.

# 2025-06-13 12:38:03.110845
+You forgot literally the single most important thing - It should be possible to also configure POST requests. Like request body, content type. What's important.

# 2025-06-13 12:44:02.921944
+/undo

# 2025-06-13 12:44:06.550566
+/quit
